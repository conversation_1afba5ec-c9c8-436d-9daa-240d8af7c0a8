"use client";

import { <PERSON><PERSON> } from "@nextui-org/button";
import { Input } from "@nextui-org/input";
import { Select, SelectItem } from "@nextui-org/select";
import { SearchIcon } from "@/components/icons";
import { platformConfig } from "@/lib/social-media";

interface SocialMediaFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  selectedPlatform: string;
  onPlatformChange: (platform: string) => void;
  sortBy: string;
  onSortChange: (sortBy: string) => void;
  totalPosts: number;
  filteredCount: number;
}

export default function SocialMediaFilters({
  searchTerm,
  onSearchChange,
  selectedPlatform,
  onPlatformChange,
  sortBy,
  onSortChange,
  totalPosts,
  filteredCount,
}: SocialMediaFiltersProps) {
  return (
    <div className="mb-8 space-y-6">
      {/* Search and Sort Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-stretch sm:items-center">
        <Input
          placeholder="Search posts..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          startContent={<SearchIcon className="text-default-400" />}
          className="flex-1 max-w-md"
          isClearable
          onClear={() => onSearchChange("")}
          size="lg"
        />

        <Select
          placeholder="Sort by"
          selectedKeys={[sortBy]}
          onSelectionChange={(keys) =>
            onSortChange(Array.from(keys)[0] as string)
          }
          className="w-full sm:w-48"
          size="lg"
        >
          <SelectItem key="date" value="date">
            📅 Latest First
          </SelectItem>
          <SelectItem key="engagement" value="engagement">
            🔥 Most Engaged
          </SelectItem>
        </Select>
      </div>

      {/* Platform Filter Buttons */}
      <div className="flex flex-wrap gap-2 justify-center sm:justify-start">
        <Button
          variant={selectedPlatform === "all" ? "solid" : "bordered"}
          color="primary"
          size="sm"
          onClick={() => onPlatformChange("all")}
          className="font-medium"
        >
          All Platforms
        </Button>
        {Object.entries(platformConfig).map(([key, config]) => (
          <Button
            key={key}
            variant={selectedPlatform === key ? "solid" : "bordered"}
            color={config.color}
            size="sm"
            onClick={() => onPlatformChange(key)}
            startContent={<span className="text-sm">{config.icon}</span>}
            className="font-medium"
          >
            <span className="hidden sm:inline">{config.name}</span>
            <span className="sm:hidden">{config.name.split(" ")[0]}</span>
          </Button>
        ))}
      </div>

      {/* Results count */}
      <div className="bg-default-50 dark:bg-default-100/20 rounded-lg p-4 border-1 border-default-200">
        <div className="text-sm text-default-600 font-medium">
          <span className="text-primary font-bold">{filteredCount}</span> of{" "}
          <span className="text-secondary font-bold">{totalPosts}</span> posts
          {searchTerm && (
            <span className="block sm:inline sm:ml-2 text-primary">
              matching "{searchTerm}"
            </span>
          )}
          {selectedPlatform !== "all" && (
            <span className="block sm:inline sm:ml-2 text-secondary">
              on{" "}
              {
                platformConfig[selectedPlatform as keyof typeof platformConfig]
                  ?.name
              }
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
