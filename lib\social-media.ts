// Types for social media posts
export interface SocialMediaPost {
  id: string;
  platform: "reddit" | "linkedin-personal" | "linkedin-company" | "twitter";
  title: string;
  content: string;
  url: string;
  date: string;
  engagement?: {
    likes?: number;
    comments?: number;
    shares?: number;
    upvotes?: number;
  };
  tags?: string[];
  subreddit?: string; // For Reddit posts
}

export interface SocialMediaData {
  posts: SocialMediaPost[];
  metadata: {
    lastUpdated: string;
    totalPosts: number;
    platforms: Record<string, number>;
  };
}

// Platform configurations
export const platformConfig = {
  reddit: {
    name: "Reddit",
    color: "warning" as const,
    icon: "🔴",
    bgColor: "bg-orange-50 dark:bg-orange-950/20",
    textColor: "text-orange-600 dark:text-orange-400",
  },
  "linkedin-personal": {
    name: "LinkedIn Personal",
    color: "primary" as const,
    icon: "👤",
    bgColor: "bg-blue-50 dark:bg-blue-950/20",
    textColor: "text-blue-600 dark:text-blue-400",
  },
  "linkedin-company": {
    name: "LinkedIn Company",
    color: "secondary" as const,
    icon: "🏢",
    bgColor: "bg-purple-50 dark:bg-purple-950/20",
    textColor: "text-purple-600 dark:text-purple-400",
  },
  twitter: {
    name: "X (Twitter)",
    color: "default" as const,
    icon: "🐦",
    bgColor: "bg-gray-50 dark:bg-gray-950/20",
    textColor: "text-gray-600 dark:text-gray-400",
  },
};

// Utility functions
export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

export const formatRelativeDate = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) return "Today";
  if (diffInDays === 1) return "Yesterday";
  if (diffInDays < 7) return `${diffInDays} days ago`;
  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
  return `${Math.floor(diffInDays / 365)} years ago`;
};

export const getTotalEngagement = (post: SocialMediaPost): number => {
  if (!post.engagement) return 0;
  
  const { likes = 0, comments = 0, shares = 0, upvotes = 0 } = post.engagement;
  return likes + comments + shares + upvotes;
};

export const sortPostsByDate = (posts: SocialMediaPost[], ascending = false): SocialMediaPost[] => {
  return [...posts].sort((a, b) => {
    const dateA = new Date(a.date).getTime();
    const dateB = new Date(b.date).getTime();
    return ascending ? dateA - dateB : dateB - dateA;
  });
};

export const sortPostsByEngagement = (posts: SocialMediaPost[], ascending = false): SocialMediaPost[] => {
  return [...posts].sort((a, b) => {
    const engagementA = getTotalEngagement(a);
    const engagementB = getTotalEngagement(b);
    return ascending ? engagementA - engagementB : engagementB - engagementA;
  });
};

export const filterPostsByPlatform = (posts: SocialMediaPost[], platform: string): SocialMediaPost[] => {
  if (platform === "all") return posts;
  return posts.filter(post => post.platform === platform);
};

export const filterPostsByDateRange = (
  posts: SocialMediaPost[], 
  startDate: string, 
  endDate: string
): SocialMediaPost[] => {
  const start = new Date(startDate).getTime();
  const end = new Date(endDate).getTime();
  
  return posts.filter(post => {
    const postDate = new Date(post.date).getTime();
    return postDate >= start && postDate <= end;
  });
};

export const searchPosts = (posts: SocialMediaPost[], searchTerm: string): SocialMediaPost[] => {
  if (!searchTerm.trim()) return posts;
  
  const term = searchTerm.toLowerCase();
  return posts.filter(post =>
    post.title.toLowerCase().includes(term) ||
    post.content.toLowerCase().includes(term) ||
    post.tags?.some(tag => tag.toLowerCase().includes(term)) ||
    (post.subreddit && post.subreddit.toLowerCase().includes(term))
  );
};

export const getPostsByTag = (posts: SocialMediaPost[], tag: string): SocialMediaPost[] => {
  return posts.filter(post => 
    post.tags?.some(postTag => postTag.toLowerCase() === tag.toLowerCase())
  );
};

export const getAllTags = (posts: SocialMediaPost[]): string[] => {
  const tagSet = new Set<string>();
  posts.forEach(post => {
    post.tags?.forEach(tag => tagSet.add(tag));
  });
  return Array.from(tagSet).sort();
};

export const getPlatformStats = (posts: SocialMediaPost[]) => {
  const stats = {
    total: posts.length,
    platforms: {} as Record<string, number>,
    totalEngagement: 0,
    averageEngagement: 0,
  };

  posts.forEach(post => {
    // Count posts per platform
    stats.platforms[post.platform] = (stats.platforms[post.platform] || 0) + 1;
    
    // Calculate total engagement
    stats.totalEngagement += getTotalEngagement(post);
  });

  stats.averageEngagement = stats.total > 0 ? Math.round(stats.totalEngagement / stats.total) : 0;

  return stats;
};

// API functions
export const loadSocialMediaPosts = async (): Promise<SocialMediaData> => {
  try {
    const response = await fetch('/social-media-posts.json');
    if (!response.ok) {
      throw new Error('Failed to load social media posts');
    }
    return await response.json();
  } catch (error) {
    console.error('Error loading social media posts:', error);
    // Return empty data structure as fallback
    return {
      posts: [],
      metadata: {
        lastUpdated: new Date().toISOString(),
        totalPosts: 0,
        platforms: {},
      },
    };
  }
};

// Helper function to generate a new post ID
export const generatePostId = (platform: string): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${platform}-${timestamp}-${random}`;
};

// Validation function for new posts
export const validatePost = (post: Partial<SocialMediaPost>): string[] => {
  const errors: string[] = [];

  if (!post.title?.trim()) {
    errors.push("Title is required");
  }

  if (!post.content?.trim()) {
    errors.push("Content is required");
  }

  if (!post.url?.trim()) {
    errors.push("URL is required");
  } else if (!isValidUrl(post.url)) {
    errors.push("URL must be a valid URL");
  }

  if (!post.platform) {
    errors.push("Platform is required");
  } else if (!Object.keys(platformConfig).includes(post.platform)) {
    errors.push("Invalid platform selected");
  }

  if (!post.date?.trim()) {
    errors.push("Date is required");
  } else if (isNaN(new Date(post.date).getTime())) {
    errors.push("Date must be a valid date");
  }

  return errors;
};

const isValidUrl = (string: string): boolean => {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
};
