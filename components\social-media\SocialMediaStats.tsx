"use client";

import { Card, CardBody } from "@nextui-org/card";
import { Chip } from "@nextui-org/chip";
import { platformConfig } from "@/lib/social-media";

interface SocialMediaStatsProps {
  stats: {
    total: number;
    platforms: Record<string, number>;
    totalEngagement: number;
    averageEngagement: number;
  };
}

export default function SocialMediaStats({ stats }: SocialMediaStatsProps) {
  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + "k";
    }
    return num.toString();
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-8">
      {/* Total Posts */}
      <Card className="bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-950/20 dark:to-primary-900/20 hover:shadow-lg transition-all duration-300 border-1 border-primary-200 dark:border-primary-800">
        <CardBody className="text-center p-6">
          <div className="text-4xl font-bold text-primary mb-2 animate-pulse">
            {stats.total}
          </div>
          <div className="text-sm text-default-600 font-medium">
            Total Posts
          </div>
          <div className="text-xs text-default-500 mt-1">
            Across all platforms
          </div>
        </CardBody>
      </Card>

      {/* Total Engagement */}
      <Card className="bg-gradient-to-br from-secondary-50 to-secondary-100 dark:from-secondary-950/20 dark:to-secondary-900/20 hover:shadow-lg transition-all duration-300 border-1 border-secondary-200 dark:border-secondary-800">
        <CardBody className="text-center p-6">
          <div className="text-4xl font-bold text-secondary mb-2">
            {formatNumber(stats.totalEngagement)}
          </div>
          <div className="text-sm text-default-600 font-medium">
            Total Engagement
          </div>
          <div className="text-xs text-default-500 mt-1">
            Likes, shares & comments
          </div>
        </CardBody>
      </Card>

      {/* Average Engagement */}
      <Card className="bg-gradient-to-br from-success-50 to-success-100 dark:from-success-950/20 dark:to-success-900/20 hover:shadow-lg transition-all duration-300 border-1 border-success-200 dark:border-success-800">
        <CardBody className="text-center p-6">
          <div className="text-4xl font-bold text-success mb-2">
            {formatNumber(stats.averageEngagement)}
          </div>
          <div className="text-sm text-default-600 font-medium">
            Avg. Engagement
          </div>
          <div className="text-xs text-default-500 mt-1">Per post average</div>
        </CardBody>
      </Card>

      {/* Platform Breakdown */}
      <Card className="bg-gradient-to-br from-warning-50 to-warning-100 dark:from-warning-950/20 dark:to-warning-900/20 hover:shadow-lg transition-all duration-300 border-1 border-warning-200 dark:border-warning-800">
        <CardBody className="p-6">
          <div className="text-sm text-default-600 mb-3 text-center font-medium">
            Platform Breakdown
          </div>
          <div className="space-y-2">
            {Object.entries(stats.platforms)
              .sort(([, a], [, b]) => b - a) // Sort by count descending
              .map(([platform, count]) => {
                const config =
                  platformConfig[platform as keyof typeof platformConfig];
                if (!config) return null;

                const percentage = ((count / stats.total) * 100).toFixed(0);

                return (
                  <div
                    key={platform}
                    className="flex justify-between items-center"
                  >
                    <div className="flex items-center gap-2 flex-1">
                      <span className="text-lg">{config.icon}</span>
                      <div className="flex-1 min-w-0">
                        <div className="text-xs font-medium truncate">
                          {config.name}
                        </div>
                        <div className="text-xs text-default-500">
                          {percentage}%
                        </div>
                      </div>
                    </div>
                    <Chip
                      size="sm"
                      variant="flat"
                      color={config.color}
                      className="ml-2 font-semibold"
                    >
                      {count}
                    </Chip>
                  </div>
                );
              })}
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
