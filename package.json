{"name": "next-app-template", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx -c .eslintrc.json --fix"}, "dependencies": {"@nextui-org/button": "2.0.38", "@nextui-org/code": "2.0.33", "@nextui-org/input": "2.2.5", "@nextui-org/kbd": "2.0.34", "@nextui-org/link": "2.0.35", "@nextui-org/listbox": "2.1.27", "@nextui-org/navbar": "2.0.37", "@nextui-org/react": "^2.4.8", "@nextui-org/snippet": "2.0.43", "@nextui-org/switch": "2.0.34", "@nextui-org/system": "2.2.6", "@nextui-org/theme": "2.2.11", "@react-aria/ssr": "3.9.4", "@react-aria/visually-hidden": "3.8.12", "@vercel/speed-insights": "^1.1.0", "clsx": "2.1.1", "fluid-simulation-react": "^1.0.5", "framer-motion": "~11.1.1", "gsap": "^3.12.5", "intl-messageformat": "^10.5.0", "locomotive-scroll": "^5.0.0-beta.21", "next": "14.2.4", "next-seo": "^6.6.0", "next-themes": "^0.2.1", "react": "18.3.1", "react-dom": "18.3.1", "react-fast-marquee": "^1.6.5", "react-slick": "^0.30.2", "slick-carousel": "^1.8.1"}, "devDependencies": {"@types/node": "20.5.7", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@types/react-slick": "^0.23.13", "@typescript-eslint/eslint-plugin": "7.2.0", "@typescript-eslint/parser": "7.2.0", "autoprefixer": "10.4.19", "eslint": "^8.57.0", "eslint-config-next": "14.2.1", "eslint-config-prettier": "^8.2.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^3.2.0", "postcss": "8.4.38", "tailwind-variants": "0.1.20", "tailwindcss": "3.4.3", "typescript": "5.0.4"}}