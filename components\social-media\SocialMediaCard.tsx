"use client";

import { <PERSON>, CardBody, CardHeader } from "@nextui-org/card";
import { Chip } from "@nextui-org/chip";
import { Divider } from "@nextui-org/divider";
import {
  SocialMediaPost,
  platformConfig,
  formatDate,
  formatRelativeDate,
  getTotalEngagement,
} from "@/lib/social-media";

interface SocialMediaCardProps {
  post: SocialMediaPost;
  showRelativeDate?: boolean;
}

export default function SocialMediaCard({
  post,
  showRelativeDate = false,
}: SocialMediaCardProps) {
  const config = platformConfig[post.platform];
  const totalEngagement = getTotalEngagement(post);

  const handleCardClick = () => {
    window.open(post.url, "_blank");
  };

  const renderEngagementMetrics = () => {
    if (!post.engagement) return null;

    const metrics = [];

    if (post.engagement.likes) {
      metrics.push({
        icon: "❤️",
        value: post.engagement.likes,
        label: "likes",
      });
    }
    if (post.engagement.upvotes) {
      metrics.push({
        icon: "⬆️",
        value: post.engagement.upvotes,
        label: "upvotes",
      });
    }
    if (post.engagement.comments) {
      metrics.push({
        icon: "💬",
        value: post.engagement.comments,
        label: "comments",
      });
    }
    if (post.engagement.shares) {
      metrics.push({
        icon: "🔄",
        value: post.engagement.shares,
        label: "shares",
      });
    }

    return (
      <div className="flex justify-between text-xs text-default-500">
        {metrics.map((metric, index) => (
          <span key={index} title={`${metric.value} ${metric.label}`}>
            {metric.icon} {metric.value}
          </span>
        ))}
        {totalEngagement > 0 && (
          <span className="font-medium text-primary">
            Total: {totalEngagement}
          </span>
        )}
      </div>
    );
  };

  return (
    <Card
      className={`hover:shadow-xl hover:scale-[1.02] transition-all duration-300 ${config.bgColor} border-1 border-default-200 cursor-pointer group hover:border-primary-300 dark:hover:border-primary-600`}
      isPressable
      onPress={handleCardClick}
    >
      <CardHeader className="pb-2 px-4 pt-4">
        <div className="flex justify-between items-start w-full">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <span className="text-lg group-hover:scale-110 transition-transform duration-300">
              {config.icon}
            </span>
            <Chip
              color={config.color}
              variant="flat"
              size="sm"
              className="font-medium shrink-0"
            >
              {config.name}
            </Chip>
          </div>
          <div className="text-right shrink-0 ml-2">
            <div className="text-xs text-default-500 font-medium">
              {showRelativeDate
                ? formatRelativeDate(post.date)
                : formatDate(post.date)}
            </div>
            {post.subreddit && (
              <div className="text-xs text-orange-600 dark:text-orange-400 mt-1 font-medium">
                {post.subreddit}
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardBody className="pt-0 px-4 pb-4">
        <h3 className="font-semibold text-lg mb-3 line-clamp-2 group-hover:text-primary transition-colors duration-300 leading-tight">
          {post.title}
        </h3>

        <p className="text-default-600 text-sm mb-4 line-clamp-3 leading-relaxed">
          {post.content}
        </p>

        {post.tags && post.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {post.tags.slice(0, 4).map((tag, index) => (
              <Chip
                key={index}
                size="sm"
                variant="flat"
                color="default"
                className="text-xs"
              >
                {tag}
              </Chip>
            ))}
            {post.tags.length > 4 && (
              <Chip
                size="sm"
                variant="flat"
                color="default"
                className="text-xs"
              >
                +{post.tags.length - 4} more
              </Chip>
            )}
          </div>
        )}

        <Divider className="mb-3" />

        {renderEngagementMetrics()}
      </CardBody>
    </Card>
  );
}
