"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@nextui-org/button";
import NextLink from "next/link";
import SocialMediaCard from "@/components/social-media/SocialMediaCard";
import SocialMediaFilters from "@/components/social-media/SocialMediaFilters";
import SocialMediaStats from "@/components/social-media/SocialMediaStats";
import AdvancedFilters, {
  AdvancedFilterState,
} from "@/components/social-media/AdvancedFilters";
import {
  SocialMediaPost,
  platformConfig,
  loadSocialMediaPosts,
  formatDate,
  formatRelativeDate,
  sortPostsByDate,
  sortPostsByEngagement,
  filterPostsByPlatform,
  filterPostsByDateRange,
  searchPosts,
  getPlatformStats,
  getTotalEngagement,
  getPostsByTag,
} from "@/lib/social-media";

export default function SocialUpdatesPage() {
  const [posts, setPosts] = useState<SocialMediaPost[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<SocialMediaPost[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedPlatform, setSelectedPlatform] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("date");
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<any>(null);
  const [advancedFilters, setAdvancedFilters] = useState<AdvancedFilterState>({
    dateRange: { start: "", end: "" },
    selectedTags: [],
    minEngagement: 0,
    maxEngagement: 1000,
  });

  // Load posts from JSON file
  useEffect(() => {
    const loadPosts = async () => {
      try {
        const data = await loadSocialMediaPosts();
        const sortedPosts = sortPostsByDate(data.posts);
        setPosts(sortedPosts);
        setFilteredPosts(sortedPosts);
        setStats(getPlatformStats(sortedPosts));
        setLoading(false);
      } catch (error) {
        console.error("Error loading posts:", error);
        setLoading(false);
      }
    };

    loadPosts();
  }, []);

  // Filter and sort posts based on search, platform, and sort criteria
  useEffect(() => {
    let filtered = filterPostsByPlatform(posts, selectedPlatform);
    filtered = searchPosts(filtered, searchTerm);

    // Apply advanced filters
    if (advancedFilters.dateRange.start || advancedFilters.dateRange.end) {
      const startDate = advancedFilters.dateRange.start || "1900-01-01";
      const endDate = advancedFilters.dateRange.end || "2100-12-31";
      filtered = filterPostsByDateRange(filtered, startDate, endDate);
    }

    if (advancedFilters.selectedTags.length > 0) {
      filtered = filtered.filter((post) =>
        advancedFilters.selectedTags.some((tag) => post.tags?.includes(tag))
      );
    }

    if (
      advancedFilters.minEngagement > 0 ||
      advancedFilters.maxEngagement < 1000
    ) {
      filtered = filtered.filter((post) => {
        const engagement = getTotalEngagement(post);
        return (
          engagement >= advancedFilters.minEngagement &&
          engagement <= advancedFilters.maxEngagement
        );
      });
    }

    // Apply sorting
    if (sortBy === "date") {
      filtered = sortPostsByDate(filtered);
    } else if (sortBy === "engagement") {
      filtered = sortPostsByEngagement(filtered);
    }

    setFilteredPosts(filtered);
  }, [posts, searchTerm, selectedPlatform, sortBy, advancedFilters]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading social media updates...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-default-50/50 to-primary-50/30 py-8">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="mb-6">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-primary via-secondary to-success bg-clip-text text-transparent animate-pulse">
              Social Media Updates
            </h1>
            <p className="text-lg md:text-xl text-default-600 max-w-3xl mx-auto leading-relaxed">
              Stay updated with our latest posts across Reddit, LinkedIn, and X
              (Twitter). Follow our journey as we share insights, tips, and
              showcase our work in the design community.
            </p>
          </div>

          {/* Stats */}
          {stats && <SocialMediaStats stats={stats} />}
        </div>

        {/* Filters */}
        <SocialMediaFilters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          selectedPlatform={selectedPlatform}
          onPlatformChange={setSelectedPlatform}
          sortBy={sortBy}
          onSortChange={setSortBy}
          totalPosts={posts.length}
          filteredCount={filteredPosts.length}
        />

        {/* Admin Link and Advanced Filters */}
        <div className="flex justify-between items-center mb-6">
          <AdvancedFilters
            posts={posts}
            onFiltersChange={setAdvancedFilters}
            currentFilters={advancedFilters}
          />
          <NextLink href="/social-updates/admin">
            <Button size="sm" variant="bordered" color="secondary">
              + Add New Post
            </Button>
          </NextLink>
        </div>

        {/* Posts Grid */}
        {filteredPosts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 auto-rows-fr">
            {filteredPosts.map((post) => (
              <SocialMediaCard key={post.id} post={post} showRelativeDate />
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold mb-2 text-default-700">
                No posts found
              </h3>
              <p className="text-default-500 mb-6">
                No posts match your current search criteria. Try adjusting your
                filters or search terms.
              </p>
              <Button
                color="primary"
                variant="bordered"
                onClick={() => {
                  setSearchTerm("");
                  setSelectedPlatform("all");
                  setAdvancedFilters({
                    dateRange: { start: "", end: "" },
                    selectedTags: [],
                    minEngagement: 0,
                    maxEngagement: 1000,
                  });
                }}
              >
                Clear All Filters
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
