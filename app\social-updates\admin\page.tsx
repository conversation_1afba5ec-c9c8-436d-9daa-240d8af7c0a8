"use client";

import { useState } from "react";
import { <PERSON>, CardBody, CardHeader } from "@nextui-org/card";
import { <PERSON><PERSON> } from "@nextui-org/button";
import { Input } from "@nextui-org/input";
import { Textarea } from "@nextui-org/input";
import { Select, SelectItem } from "@nextui-org/select";
import { Chip } from "@nextui-org/chip";
import { Divider } from "@nextui-org/divider";
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
} from "@nextui-org/modal";
import NextLink from "next/link";
import {
  SocialMediaPost,
  platformConfig,
  generatePostId,
  validatePost,
} from "@/lib/social-media";

export default function SocialUpdatesAdminPage() {
  const [formData, setFormData] = useState<Partial<SocialMediaPost>>({
    platform: "reddit",
    title: "",
    content: "",
    url: "",
    date: new Date().toISOString().split("T")[0],
    engagement: {},
    tags: [],
  });

  const [tagInput, setTagInput] = useState("");
  const [errors, setErrors] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [jsonOutput, setJsonOutput] = useState("");
  const { isOpen, onOpen, onClose } = useDisclosure();

  const handleInputChange = (field: keyof SocialMediaPost, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear errors when user starts typing
    if (errors.length > 0) {
      setErrors([]);
    }
  };

  const handleEngagementChange = (field: string, value: string) => {
    const numValue = value === "" ? undefined : parseInt(value);
    setFormData((prev) => ({
      ...prev,
      engagement: {
        ...prev.engagement,
        [field]: numValue,
      },
    }));
  };

  const addTag = () => {
    if (tagInput.trim() && !formData.tags?.includes(tagInput.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...(prev.tags || []), tagInput.trim()],
      }));
      setTagInput("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags?.filter((tag) => tag !== tagToRemove) || [],
    }));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    // Validate the form
    const validationErrors = validatePost(formData);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      setIsSubmitting(false);
      return;
    }

    // Generate ID and create the post object
    const newPost: SocialMediaPost = {
      ...(formData as SocialMediaPost),
      id: generatePostId(formData.platform!),
    };

    // Generate JSON output for manual addition to the file
    const jsonString = JSON.stringify(newPost, null, 2);
    setJsonOutput(jsonString);
    onOpen();

    setIsSubmitting(false);
  };

  const resetForm = () => {
    setFormData({
      platform: "reddit",
      title: "",
      content: "",
      url: "",
      date: new Date().toISOString().split("T")[0],
      engagement: {},
      tags: [],
    });
    setTagInput("");
    setErrors([]);
    setJsonOutput("");
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(jsonOutput);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-default-50/50 to-secondary-50/30 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">Add New Social Media Post</h1>
          <p className="text-default-600 mb-4">
            Use this form to add new social media posts to your collection.
          </p>
          <NextLink href="/social-updates">
            <Button variant="bordered" color="primary">
              ← Back to Social Updates
            </Button>
          </NextLink>
        </div>

        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <h2 className="text-xl font-semibold">Post Details</h2>
          </CardHeader>
          <CardBody className="space-y-6">
            {/* Platform Selection */}
            <Select
              label="Platform"
              placeholder="Select a platform"
              selectedKeys={formData.platform ? [formData.platform] : []}
              onSelectionChange={(keys) =>
                handleInputChange("platform", Array.from(keys)[0])
              }
              isRequired
            >
              {Object.entries(platformConfig).map(([key, config]) => (
                <SelectItem
                  key={key}
                  value={key}
                  startContent={<span>{config.icon}</span>}
                >
                  {config.name}
                </SelectItem>
              ))}
            </Select>

            {/* Title */}
            <Input
              label="Title"
              placeholder="Enter post title"
              value={formData.title || ""}
              onChange={(e) => handleInputChange("title", e.target.value)}
              isRequired
            />

            {/* Content */}
            <Textarea
              label="Content"
              placeholder="Enter post content"
              value={formData.content || ""}
              onChange={(e) => handleInputChange("content", e.target.value)}
              minRows={4}
              isRequired
            />

            {/* URL */}
            <Input
              label="URL"
              placeholder="https://..."
              value={formData.url || ""}
              onChange={(e) => handleInputChange("url", e.target.value)}
              isRequired
            />

            {/* Date */}
            <Input
              label="Date"
              type="date"
              value={formData.date || ""}
              onChange={(e) => handleInputChange("date", e.target.value)}
              isRequired
            />

            <Divider />

            {/* Engagement Metrics */}
            <div>
              <h3 className="text-lg font-medium mb-4">
                Engagement Metrics (Optional)
              </h3>
              <div className="grid grid-cols-2 gap-4">
                {formData.platform === "reddit" ? (
                  <>
                    <Input
                      label="Upvotes"
                      type="number"
                      placeholder="0"
                      value={formData.engagement?.upvotes?.toString() || ""}
                      onChange={(e) =>
                        handleEngagementChange("upvotes", e.target.value)
                      }
                    />
                    <Input
                      label="Comments"
                      type="number"
                      placeholder="0"
                      value={formData.engagement?.comments?.toString() || ""}
                      onChange={(e) =>
                        handleEngagementChange("comments", e.target.value)
                      }
                    />
                  </>
                ) : (
                  <>
                    <Input
                      label="Likes"
                      type="number"
                      placeholder="0"
                      value={formData.engagement?.likes?.toString() || ""}
                      onChange={(e) =>
                        handleEngagementChange("likes", e.target.value)
                      }
                    />
                    <Input
                      label="Comments"
                      type="number"
                      placeholder="0"
                      value={formData.engagement?.comments?.toString() || ""}
                      onChange={(e) =>
                        handleEngagementChange("comments", e.target.value)
                      }
                    />
                    <Input
                      label="Shares"
                      type="number"
                      placeholder="0"
                      value={formData.engagement?.shares?.toString() || ""}
                      onChange={(e) =>
                        handleEngagementChange("shares", e.target.value)
                      }
                    />
                  </>
                )}
              </div>
            </div>

            <Divider />

            {/* Tags */}
            <div>
              <h3 className="text-lg font-medium mb-4">Tags</h3>
              <div className="flex gap-2 mb-4">
                <Input
                  placeholder="Add a tag"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && addTag()}
                  className="flex-1"
                />
                <Button onClick={addTag} color="primary" variant="bordered">
                  Add
                </Button>
              </div>

              {formData.tags && formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map((tag, index) => (
                    <Chip
                      key={index}
                      onClose={() => removeTag(tag)}
                      variant="flat"
                      color="primary"
                    >
                      {tag}
                    </Chip>
                  ))}
                </div>
              )}
            </div>

            {/* Errors */}
            {errors.length > 0 && (
              <div className="bg-danger-50 border border-danger-200 rounded-lg p-4">
                <h4 className="text-danger font-medium mb-2">
                  Please fix the following errors:
                </h4>
                <ul className="list-disc list-inside text-danger text-sm">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Actions */}
            <div className="flex gap-4 pt-4">
              <Button
                color="primary"
                onClick={handleSubmit}
                isLoading={isSubmitting}
                className="flex-1"
              >
                Generate JSON
              </Button>
              <Button variant="bordered" onClick={resetForm} className="flex-1">
                Reset Form
              </Button>
            </div>
          </CardBody>
        </Card>

        {/* JSON Output Modal */}
        <Modal isOpen={isOpen} onClose={onClose} size="3xl">
          <ModalContent>
            <ModalHeader>
              <h3>Generated JSON</h3>
            </ModalHeader>
            <ModalBody>
              <p className="text-sm text-default-600 mb-4">
                Copy this JSON object and add it to the "posts" array in your
                <code className="bg-default-100 px-1 rounded">
                  public/social-media-posts.json
                </code>{" "}
                file.
              </p>
              <div className="bg-default-100 p-4 rounded-lg">
                <pre className="text-sm overflow-auto max-h-96">
                  <code>{jsonOutput}</code>
                </pre>
              </div>
            </ModalBody>
            <ModalFooter>
              <Button variant="bordered" onPress={onClose}>
                Close
              </Button>
              <Button color="primary" onPress={copyToClipboard}>
                Copy to Clipboard
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </div>
  );
}
