"use client";

import { useState } from "react";
import { Card, CardBody, CardHeader } from "@nextui-org/card";
import { But<PERSON> } from "@nextui-org/button";
import { Input } from "@nextui-org/input";
import { Select, SelectItem } from "@nextui-org/select";
import { Chip } from "@nextui-org/chip";
import { Divider } from "@nextui-org/divider";
import { <PERSON>dal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure } from "@nextui-org/modal";
import { getAllTags } from "@/lib/social-media";

interface AdvancedFiltersProps {
  posts: any[];
  onFiltersChange: (filters: AdvancedFilterState) => void;
  currentFilters: AdvancedFilterState;
}

export interface AdvancedFilterState {
  dateRange: {
    start: string;
    end: string;
  };
  selectedTags: string[];
  minEngagement: number;
  maxEngagement: number;
}

export default function AdvancedFilters({ posts, onFiltersChange, currentFilters }: AdvancedFiltersProps) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [localFilters, setLocalFilters] = useState<AdvancedFilterState>(currentFilters);
  const allTags = getAllTags(posts);

  const handleApplyFilters = () => {
    onFiltersChange(localFilters);
    onClose();
  };

  const handleResetFilters = () => {
    const resetFilters: AdvancedFilterState = {
      dateRange: { start: "", end: "" },
      selectedTags: [],
      minEngagement: 0,
      maxEngagement: 1000,
    };
    setLocalFilters(resetFilters);
    onFiltersChange(resetFilters);
    onClose();
  };

  const toggleTag = (tag: string) => {
    setLocalFilters(prev => ({
      ...prev,
      selectedTags: prev.selectedTags.includes(tag)
        ? prev.selectedTags.filter(t => t !== tag)
        : [...prev.selectedTags, tag]
    }));
  };

  const hasActiveFilters = 
    currentFilters.dateRange.start || 
    currentFilters.dateRange.end || 
    currentFilters.selectedTags.length > 0 || 
    currentFilters.minEngagement > 0 || 
    currentFilters.maxEngagement < 1000;

  return (
    <>
      <Button
        variant={hasActiveFilters ? "solid" : "bordered"}
        color={hasActiveFilters ? "secondary" : "default"}
        size="sm"
        onPress={onOpen}
        endContent={hasActiveFilters ? <span className="text-xs">({getActiveFilterCount(currentFilters)})</span> : null}
      >
        Advanced Filters
      </Button>

      <Modal isOpen={isOpen} onClose={onClose} size="2xl" scrollBehavior="inside">
        <ModalContent>
          <ModalHeader>
            <h3>Advanced Filters</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-6">
              {/* Date Range */}
              <Card>
                <CardHeader>
                  <h4 className="text-lg font-medium">Date Range</h4>
                </CardHeader>
                <CardBody>
                  <div className="grid grid-cols-2 gap-4">
                    <Input
                      label="Start Date"
                      type="date"
                      value={localFilters.dateRange.start}
                      onChange={(e) => setLocalFilters(prev => ({
                        ...prev,
                        dateRange: { ...prev.dateRange, start: e.target.value }
                      }))}
                    />
                    <Input
                      label="End Date"
                      type="date"
                      value={localFilters.dateRange.end}
                      onChange={(e) => setLocalFilters(prev => ({
                        ...prev,
                        dateRange: { ...prev.dateRange, end: e.target.value }
                      }))}
                    />
                  </div>
                </CardBody>
              </Card>

              {/* Engagement Range */}
              <Card>
                <CardHeader>
                  <h4 className="text-lg font-medium">Engagement Range</h4>
                </CardHeader>
                <CardBody>
                  <div className="grid grid-cols-2 gap-4">
                    <Input
                      label="Minimum Engagement"
                      type="number"
                      value={localFilters.minEngagement.toString()}
                      onChange={(e) => setLocalFilters(prev => ({
                        ...prev,
                        minEngagement: parseInt(e.target.value) || 0
                      }))}
                    />
                    <Input
                      label="Maximum Engagement"
                      type="number"
                      value={localFilters.maxEngagement.toString()}
                      onChange={(e) => setLocalFilters(prev => ({
                        ...prev,
                        maxEngagement: parseInt(e.target.value) || 1000
                      }))}
                    />
                  </div>
                </CardBody>
              </Card>

              {/* Tags */}
              <Card>
                <CardHeader>
                  <h4 className="text-lg font-medium">Tags</h4>
                  <p className="text-sm text-default-500">Select tags to filter by</p>
                </CardHeader>
                <CardBody>
                  <div className="flex flex-wrap gap-2">
                    {allTags.map((tag) => (
                      <Chip
                        key={tag}
                        variant={localFilters.selectedTags.includes(tag) ? "solid" : "bordered"}
                        color={localFilters.selectedTags.includes(tag) ? "primary" : "default"}
                        className="cursor-pointer"
                        onClick={() => toggleTag(tag)}
                      >
                        {tag}
                      </Chip>
                    ))}
                  </div>
                  {localFilters.selectedTags.length > 0 && (
                    <div className="mt-4">
                      <p className="text-sm text-default-600 mb-2">Selected tags:</p>
                      <div className="flex flex-wrap gap-2">
                        {localFilters.selectedTags.map((tag) => (
                          <Chip
                            key={tag}
                            color="primary"
                            variant="flat"
                            onClose={() => toggleTag(tag)}
                          >
                            {tag}
                          </Chip>
                        ))}
                      </div>
                    </div>
                  )}
                </CardBody>
              </Card>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="bordered" onPress={handleResetFilters}>
              Reset All
            </Button>
            <Button color="primary" onPress={handleApplyFilters}>
              Apply Filters
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}

function getActiveFilterCount(filters: AdvancedFilterState): number {
  let count = 0;
  if (filters.dateRange.start || filters.dateRange.end) count++;
  if (filters.selectedTags.length > 0) count++;
  if (filters.minEngagement > 0 || filters.maxEngagement < 1000) count++;
  return count;
}
